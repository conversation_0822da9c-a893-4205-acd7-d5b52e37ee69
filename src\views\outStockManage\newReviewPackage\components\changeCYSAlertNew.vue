<template>
   <xyy-dialog
        ref="dialogChangeCYSAlert"
        title="提示"
        width="40%"
        :visible.sync="outerVisible"
      >
      {{ deliveryMsg }}
         <span slot="footer" class="dialog-footer">
            <el-button @click="cancelHandler">取 消</el-button>
            <el-button type="primary" @click="changeCarrierConfirm()">确 定</el-button>
        </span>
      </xyy-dialog>
</template>

<script>
import {
    changeCarrier,
    changeCarrierConfirm
} from "@/api/outstock/fhdb";

export default{
    data(){
        return{
            outerVisible: false,
            deliveryMsg:"",
            formData:{
                orderCode:""
            },
            confrimData:null
        }
    },
    methods:{
        open(val, formInfo){
            this.deliveryMsg = ''
            this.formData.orderCode = ''
            this.confrimData = null
            this.$refs.dialogChangeCYSAlert.open();
            this.formData.orderCode = val
            this.deliveryMsg = `是否更改为${formInfo.carrierName}${formInfo.productName}`;
            this.confrimData = formInfo
        },
        
        // 关闭弹窗
        close() {
            this.$refs.dialogChangeCYSAlert.close();
        },
        
        cancelHandler(){
            this.close()
        },
        
        changeCarrier() {
            changeCarrier(this.formData).then((res) => {
                const { result, msg, code } = res;
                if (code === 0) {
                    this.deliveryMsg = `是否更改为${result.carrierName}${result.productName}`;
                    this.confrimData = result
                } else {
                this.$message.error(msg);
                }
            });
        },
        
        changeCarrierConfirm() {
            this.confrimData.orderCode = this.formData.orderCode
            changeCarrierConfirm(this.confrimData).then((res) => {
                const { result, msg, code } = res;
                if (code === 0) {
                    this.$message.success("承运商更换成功");
                    this.close()
                } else {
                    this.$message.error(msg);
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
</style>
