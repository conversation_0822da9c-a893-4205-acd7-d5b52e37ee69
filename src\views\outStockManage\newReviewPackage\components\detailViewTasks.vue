<template>
  <div>
    <xyy-dialog ref="dialogTableVisible" title="任务明细" width="1200px" @on-close="closeDialog">
      <!-- table 组件 -->
      <vxe-table
        ref="tasksDetailTable"
        highlight-current-row
        highlight-hover-row
        height="500"
        class="tasks-detail-table"
        :loading="loading"
        :data="tasksDetailData"
        :pager-config="tablePage"
        @page-change="handlePageChange"
      >
        <vxe-table-column type="seq" title="序号" width="80" />
        <vxe-table-column field="productCode" title="商品编码" width="120" />
        <vxe-table-column field="productName" title="商品名称" width="150" />
        <vxe-table-column field="batchNumber" title="批号" width="120" />
        <vxe-table-column field="specifications" title="规格" width="120" />
        <vxe-table-column field="manufacturer" title="生产厂家" width="150" />
        <vxe-table-column field="reviewNumber" title="复核数量" width="100" />
        <vxe-table-column field="reviewStatus" title="复核状态" width="100" :formatter="reviewStatusFormatter" />
        <vxe-table-column field="packingUnit" title="包装单位" width="100" />
        <vxe-table-column field="goodsAllocation" title="货位" width="100" />
      </vxe-table>
      
      <div class="pager">
        <vxe-pager
          border
          :current-page="tablePage.pageNum"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        />
      </div>
    </xyy-dialog>
  </div>
</template>

<script>
import { getPartsInReviewTaskDetail } from '@/api/outstock/fhdb'

export default {
  name: 'DetailViewTasks',
  data() {
    return {
      loading: false,
      tasksDetailData: [],
      tablePage: {
        total: 0,
        pageNum: 1,
        pageSize: 100
      },
      currentRow: null
    }
  },
  methods: {
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage
      this.tablePage.pageSize = pageSize
      this.findDetailList()
    },
    
    // 获取明细列表
    findDetailList() {
      if (!this.currentRow) return
      
      this.loading = true
      const { pageNum, pageSize } = this.tablePage
      const params = {
        pageNum,
        pageSize,
        orderCode: this.currentRow.orderCode,
        allocationCode: this.currentRow.allocationCode
      }
      
      getPartsInReviewTaskDetail(params).then(res => {
        this.loading = false
        const { code, msg, result } = res
        if (code === 0) {
          const { list, pageNum, total } = result
          this.tablePage.pageNum = pageNum
          this.tablePage.total = total
          this.tasksDetailData = list
        } else {
          this.$message.error(msg)
        }
      })
    },
    
    // 关闭弹窗
    close() {
      this.$refs.dialogTableVisible.close()
    },
    
    // 禁止空白关闭弹窗
    closeDialog() {
      this.$emit('on-before-close')
    },
    
    // 打开弹窗
    open(row) {
      this.currentRow = row
      this.tablePage = {
        total: 0,
        pageNum: 1,
        pageSize: 100
      }
      this.findDetailList()
      this.$refs.dialogTableVisible.open()
    },
    
    // 复核状态格式化
    reviewStatusFormatter({ cellValue }) {
      const statusMap = {
        0: '待复核',
        1: '复核中',
        2: '已复核'
      }
      return statusMap[cellValue] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.pager {
  margin-top: 20px;
  text-align: center;
}
</style>
