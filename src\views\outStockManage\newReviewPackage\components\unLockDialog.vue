<template>
  <xyy-dialog
    ref="dialogTableVisible"
    title="解锁"
    width="400px"
    @on-close="onClose"
  >
    <div style="height:200px;">
      <el-form
        v-model="formData"
        :label-width="'120px'"
      >
        <el-col
          :lg="24"
          :md="24"
        >
          <el-form-item label="工号">
            <el-input
              ref="staffNum"
              v-model="formData.staffNum"
              placeholder="请输入工号"
              @keyup.enter.native="passwordFocus"
            />
          </el-form-item>
        </el-col>
        <el-col
          :lg="24"
          :md="24"
        >
          <el-form-item label="密码">
            <el-input
              ref="password"
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              @keyup.enter.native="sureBtnClick"
            />
          </el-form-item>
        </el-col>
      </el-form>
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="cancelBtnClick">取消</el-button>
      <el-button
        type="primary"
        @click="sureBtnClick"
      >确定</el-button>
    </span>
  </xyy-dialog>
</template>

<script>
import { getUserInfo } from "@/api/system/user.js";

export default {
  data() {
    return {
      formData: {
        staffNum: "",
        password: "",
      },
    };
  },
  methods: {
    open() {
      this.$refs.dialogTableVisible.open();
      this.init();
      this.$nextTick(() => {
        this.$refs.staffNum.focus();
      });
    },
    
    passwordFocus() {
      this.$refs.password.focus();
    },
    
    onClose() {},
    
    cancelBtnClick() {
      this.$refs.dialogTableVisible.close();
    },
    
    sureBtnClick() {
      if (!this.formData.staffNum) {
        this.$message.warning("请输入工号");
        return;
      }
      if (!this.formData.password) {
        this.$message.warning("请输入密码");
        return;
      }
      
      this.apiGetUserInfo();
    },
    
    // API调用
    apiGetUserInfo() {
      getUserInfo({
        staffNum: this.formData.staffNum,
        password: this.formData.password,
      }).then((res) => {
        const { code, msg, result } = res;
        if (code !== 0) {
          this.$message.error(msg);
        } else {
          this.$message.success("验证成功");
          this.$refs.dialogTableVisible.close();
          this.$emit("on-close", result);
        }
      });
    },
    
    init() {
      this.formData = {
        staffNum: "",
        password: "",
      };
    },
  },
};
</script>

<style scoped>
</style>
