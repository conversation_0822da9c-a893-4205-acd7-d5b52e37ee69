<template>
    <div>
      <xyy-dialog
        ref="dialogTableVisible"
        title="商品列表"
        width="40%"
        :visible.sync="outerVisible"
      >
        <!-- table 组件 -->
        <vxe-table
          ref="exceptionSubmissionTable"
          highlight-current-row
          highlight-hover-row
          height="300px"
          class="exception-submission-table"
          :loading="loading"
          :data="goodsData"
          @cell-dblclick="handleCellClick"
        >
          <vxe-table-column type="seq" title="序号" width="80" />
          <vxe-table-column field="productCode" title="商品编码" width="200" />
          <vxe-table-column field="productName" title="商品名称" width="200" />
          <vxe-table-column field="batchNumber" title="批号" width="245" />
        </vxe-table>
      </xyy-dialog>
    </div>
  </template>
  
  <script>
import { getExceptionGoodsList } from '@/api/outstock/fhdb'

  export default {
    name: "goodsDialog",
    data() {
      return {
        show: false,
        loading: false,
        goodsData: [],
        outerVisible: false,
      };
    },
    methods: {
      // 关闭弹窗
      close() {
        this.$refs.dialogTableVisible.close();
      },
      
      // 打开弹窗
      open(params) {
        this.loading = true;
        
        getExceptionGoodsList({packageBarCode:params[1]}).then(res => {
          this.loading = false
          const { code, msg, result } = res
          
          const singleArray = [];
          if (params[0].length>=0) {
            params[0].forEach( e => {
              let itemObj = {
                batchNumber: e.batchNumber,
                productCode: e.productCode
              }
              singleArray.push(itemObj)
            })
          }
          
          if (code === 0) {
            if(result.length>0){
              let localData = result.filter(item => !singleArray.some(data => (data.productCode === item.productCode && data.batchNumber === item.batchNumber)))
              this.goodsData = localData
            }
          }
        })
        this.$refs.dialogTableVisible.open();
      },
      
      unique(arr) {
        return arr.filter(function(item, index, arr) {
          return arr.indexOf(item, 0) === index;
        });
      },
      
      // 点击当前行
      handleCellClick(row, event, column) {
        let rowChanged = Object.assign(row.row, {"isResultGoods": true},{"exceptionCause":"2"})
        this.$emit('checkGoodsData', rowChanged)
        this.goodsData.splice(this.goodsData.indexOf(row.row),1)
        this.$refs.dialogTableVisible.close()
      },
      
      // 重新回写从异常列表删除的数据
      overWriteGoodsData(params){
        if(params.length>=0){
          this.goodsData = []
          params.forEach(e=>{
            this.goodsData.push(e)
          }) 
        }
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  </style>
