<template>
  <div>
    <xyy-dialog
      ref="dialogTableVisible"
      title="追溯码扫描"
      width="1000px"
      @on-close="closeDialog"
    >
      <!-- 默认输入框 -->
      <el-form :model="fixedInformation">
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="单据编号:"
              label-width="80px"
            >
              <el-input
                v-model="fixedInformation.receiptNumber"
                :disabled="true"
                placeholder="单据编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="业主名称:"
              label-width="100px"
            >
              <el-input
                v-model="fixedInformation.ownerName"
                :disabled="true"
                placeholder="业主名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="客户名称:"
              label-width="100px"
            >
              <el-input
                v-model="fixedInformation.clientName"
                :disabled="true"
                placeholder="客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 默认信息 -->
      <el-row class="scanInformationFrist">
        <el-col :span="16">
          <span class="viewText">商品名称:{{ fixedInformation.productName ? fixedInformation.productName : '' }}</span>
        </el-col>
        <el-col :span="8">
          <span>数量:{{ variableInformation.number ? variableInformation.number : '0' }}</span>
        </el-col>
      </el-row>
      
      <el-row class="scanInformation">
        <el-col :span="8">
          <span>中包装数量:{{ fixedInformation.mediumNumber ? fixedInformation.mediumNumber : '0' }}</span>
        </el-col>
        <el-col :span="8">
          <span>需扫描数量:{{ variableInformation.needScanNumber ? variableInformation.needScanNumber : '0' }}</span>
        </el-col>
        <el-col :span="8">
          <span>需扫描次数:{{ variableInformation.needScanTime ? variableInformation.needScanTime: '0' }}</span>
          <span v-show="variableInformation.needScanTime < 1 && variableInformation.needScanTime !== 0">（请切换包装规格）</span>
        </el-col>
        <el-col :span="8">
          <span>件包装数量:{{ fixedInformation.packingNumber ? fixedInformation.packingNumber : '0' }}</span>
        </el-col>
        <el-col :span="8">
          <span>已扫描数量:{{ variableInformation.scanNumber ? variableInformation.scanNumber : '0' }}</span>
        </el-col>
        <el-col :span="8">
          <span>已扫描次数:{{ variableInformation.scanTime ? variableInformation.scanTime : '0' }}</span>
        </el-col>
        <el-col :span="8">
          <span>已扫描明细:&nbsp;&nbsp;件/</span>
          <span>{{ variableInformation.big ? variableInformation.big : '0' }}</span>
          <span>&nbsp;&nbsp;中/</span>
          <span>{{ variableInformation.medium ? variableInformation.medium : '0' }}</span>
          <span>&nbsp;&nbsp;小/</span>
          <span>{{ variableInformation.small ? variableInformation.small : '0' }}</span>
        </el-col>
      </el-row>
      
      <!-- 默认信息表格 -->
      <vxe-table
        ref="goodsTable"
        height="150"
        class="tasks-table"
        :data="tableData"
        :row-class-name="rowClassName"
      >
        <vxe-table-column
          v-for="colInfo in tableColumn"
          :key="colInfo.field"
          v-bind="colInfo"
          show-overflow="title"
        />
      </vxe-table>
      
      <!-- 提交数据 -->
      <el-form
        class="submitForm"
        :model="formData"
      >
        <el-row>
          <el-col :span="5">
            <el-form-item
              label="包装规格:"
              label-width="82px"
            >
              <span>件/中/小包装</span>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="追溯码:"
              label-width="70px"
            >
              <el-input
                ref="regulatoryCode"
                v-model="formData.regulatoryCode"
                placeholder="请输入追溯码"
                clearable
                onfocus="this.select()"
                @input="validateNumber($event)"
                @keydown.enter.native="entryInformation"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="包装规格:"
              label-width="82px"
            >
              <el-select
                v-model="formData.codeLevel"
                :disabled="loading"
                @change="codeLevelChange"
              >
                <el-option
                  v-for="item in codeLevels"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="6"
            style="text-align:center"
          >
            <vxe-button
              status="primary"
              :disabled="loading"
              :loading="includeLoading"
              @click="entryInformation"
            >录 入</vxe-button>
            <vxe-button
              status="primary"
              :disabled="loading"
              @click="deleteBtnClick"
            >删 除</vxe-button>
            <vxe-button
            status="primary"
            @click="unlock"
            v-if="getPermission('btn:wms:reviewPackage:unlock')">
            解锁</vxe-button>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 提交数据展示表格 -->
      <vxe-table
        ref="submitTable"
        :loading="loading"
        height="250"
        class="tasks-table"
        :data="packagetableData"
        :checkbox-config="{trigger: 'row', highlight: false, range: true}"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-table-column
          v-for="colInfo in tableColumnPackage"
          :key="colInfo.field"
          v-bind="colInfo"
          show-overflow="title"
        />
      </vxe-table>
    </xyy-dialog>
    <un-lock-dialog ref="unLockDialog" @on-close="lockDialogClose" />
  </div>
</template>

<script>
import {
    unlockThisTask,
} from '@/api/outstock/index.js'
import unLockDialog from "./unLockDialog.vue";
import VxeTable from "vxe-table";
import XEUtils from "xe-utils";
import { review } from "@/api/outstock/fhdb";

// 时间格式化处理
VxeTable.formats.add("formatDate", function ({ cellValue }) {
  return XEUtils.toDateString(cellValue, "yyyy-MM-dd");
});

export default {
  name: "TracingCodeScan",
  components: {
    unLockDialog,
  },
  data() {
    return {
      includeLoading:false,
      defaultInformation: {},
      loading: false,
      tableColumn: [
        { type: "seq", title: "序号", width: 60 },
        { field: "productCode", title: "商品编码", width: 150 },
        { field: "batchNumber", title: "批号", width: 150 },
        {
          field: "produceTime",
          title: "生产日期",
          width: 150,
          formatter: "formatDate",
        },
        {
          field: "validDate",
          title: "有效期至",
          width: 150,
          formatter: "formatDate",
        },
        { field: "channelCode", title: "业务类型", width: 100 },
        { field: "specifications", title: "规格", width: 150 },
        { field: "manufacturer", title: "生产厂家", width: 200 },
        { field: "reviewNumber", title: "数量", width: 100 },
      ],
      tableData: [],
      formData: {
        regulatoryCode: '',
        codeLevel: '',
        pkgAmount: null,
      },
      codeLevels: [
        { code: "1", name: "小包装" },
        { code: "2", name: "中包装" },
        { code: "3", name: "大包装" },
      ],
      tableColumnPackage: [
        { type: "checkbox", width: 50 },
        { type: "seq", title: "序号", width: 60 },
        { field: "regulatoryCode", title: "追溯码", width: 425 },
        {
          field: "codeLevel",
          title: "包装类别",
          width: 425,
          formatter: this.codeLevelFormatter,
        },
      ],
      packagetableData: [],
      selectPackagetableData: [],
      fixedInformation: {},
      variableInformation: {},
      readIndex: 0,
      submitData: [],
    };
  },
  methods: {
    // 复用原有的所有方法
    lockDialogClose(userInfo) {
      const params = []
      this.tableData.forEach(item => {
          params.push({
              unlockType: 2,
              operationName: userInfo.realname,
              unlockAccount: userInfo.staffNum,
              unlockResult: true,
              taskCode: item.mergeOrderCode,
              productCode: item.productCode
          })
      })
      unlockThisTask(params).then(res => {
          const { result, msg, code } = res
          if (code === 0) {
              this.getReview("pass")
          } else {
              this.$message.error(msg)
          }
      })
    },
    
    unlock() {
        this.$refs.unLockDialog.open()
    },
    
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    },
    
    open(defaultInformation) {
      this.$refs.dialogTableVisible.open();
      this.$nextTick(() => {
        this.$refs.regulatoryCode.focus();
      });
      this.defaultInformation = defaultInformation || {};
      this.readIndex = 0;
      this.submitData = [];
      this.dataProcessing();
    },
    
    dataProcessing() {
      this.packagetableData = [];
      this.selectPackagetableData = [];
      this.tableData = this.defaultInformation.goods ? this.defaultInformation.goods : [];
      const goods = this.tableData[this.readIndex] ? this.tableData[this.readIndex] : {};
      
      this.formData = {
        regulatoryCode: "",
        codeLevel: "1",
        pkgAmount: "1",
      };
      
      this.fixedInformation = {
        receiptNumber: this.defaultInformation.allocationCode,
        ownerName: goods.ownerName ? goods.ownerName : "",
        clientName: goods.clientName ? goods.clientName : "",
        productName: goods.productName ? goods.productName : "",
        mediumNumber: goods.maxMediumPacking ? goods.maxMediumPacking : "",
        packingNumber: goods.maxLargePacking ? goods.maxLargePacking : "",
      };
      
      const number = goods.reviewNumber ? goods.reviewNumber : 0;
      this.variableInformation = {
        number: number,
        needScanNumber: number,
        needScanTime: number,
        scanNumber: 0,
        scanTime: 0,
        big: 0,
        medium: 0,
        small: 0,
      };
    },

    // 录入追溯码
    entryInformation() {
      if (this.formData.regulatoryCode === "") {
        this.$message.warning("请输入追溯码录入！");
        return;
      }
      if (this.formData.codeLevel === "") {
        this.formData.regulatoryCode = "";
        this.$message.warning("请选择包装规格录入！");
        return;
      }
      if(this.tableData[this.readIndex].largeCategoryCode != 202){
        if (
        this.formData.regulatoryCode.length !== 20 ||
        this.formData.regulatoryCode.substr(0, 1) !== "8"
      ) {
        this.formData.regulatoryCode = "";
        this.$message.warning("该条码不正确，请检查是否扫码正确！");
        return;
      }
    }

      if (!this.defaultInformation.allocationCode) {
        this.formData.regulatoryCode = "";
        this.$message.error("单据编号有误，请关闭弹窗后，刷新数据！");
        return;
      }

      // 判断是否有重复
      let filter = true;
      this.packagetableData.map((choose) => {
        if (choose.regulatoryCode === this.formData.regulatoryCode) {
          filter = false;
        }
      });

      this.submitData.map((choose) => {
        if (choose.regulatoryCode === this.formData.regulatoryCode) {
          filter = false;
        }
      });

      if (filter === false) {
        this.formData.regulatoryCode = "";
        this.$message.warning(
          this.formData.regulatoryCode + " 此追溯码 该批次或其他批次已录入！"
        );
        return;
      }

      // 判断是否超出数量
      let simulationNum = 0;
      if (this.formData.codeLevel === "1") {
        simulationNum = this.variableInformation.needScanNumber - 1;
      } else if (this.formData.codeLevel === "2") {
        simulationNum =
          this.variableInformation.needScanNumber -
          this.fixedInformation.mediumNumber;
      } else if (this.formData.codeLevel === "3") {
        simulationNum =
          this.variableInformation.needScanNumber -
          this.fixedInformation.packingNumber;
      }

      if (simulationNum < 0) {
        this.formData.regulatoryCode = "";
        this.$message.warning("当前选择包装类型，其合计数量，超过总数量");
        return;
      }

      // 包装数量
      if (this.formData.codeLevel === "1") {
        this.formData.pkgAmount = 1;
      } else if (this.formData.codeLevel === "2") {
        this.formData.pkgAmount = this.fixedInformation.mediumNumber;
      } else if (this.formData.codeLevel === "3") {
        this.formData.pkgAmount = this.fixedInformation.packingNumber;
      }

      this.packagetableData.push(JSON.parse(JSON.stringify(this.formData)));
      this.formData.regulatoryCode = "";

      setTimeout(() => {
        this.$refs.submitTable.scrollTo(
          0,
          (this.packagetableData.length + 1) * 40
        );
      }, 50);

      this.toDealWithStatistics();
    },

    // 选中全部
    selectAllEvent({ checked, records }) {
      this.selectPackagetableData = records;
    },

    // 单个勾选
    selectChangeEvent({ checked, records }) {
      this.selectPackagetableData = records;
    },

    // 删除
    deleteBtnClick() {
      if (this.selectPackagetableData.length === 0) {
        this.$message.warning("请选择要删除的追溯码");
        return;
      }

      for (let i = 0; i < this.packagetableData.length; i++) {
        this.selectPackagetableData.map((choose) => {
          if (
            this.packagetableData[i].regulatoryCode === choose.regulatoryCode
          ) {
            this.packagetableData.splice(i, 1);
          }
        });
      }

      for (let i = 0; i < this.submitData.length; i++) {
        this.selectPackagetableData.map((choose) => {
          if (this.submitData[i].regulatoryCode === choose.regulatoryCode) {
            this.submitData.splice(i, 1);
          }
        });
      }

      this.selectPackagetableData = [];
      this.$message.success("删除成功");
      this.toDealWithStatistics();
    },

    // 处理上部扫描统计
    toDealWithStatistics() {
      this.variableInformation.scanTime = this.packagetableData.length;
      this.variableInformation.scanNumber = 0;
      this.variableInformation.big = 0;
      this.variableInformation.medium = 0;
      this.variableInformation.small = 0;

      this.packagetableData.map((entry) => {
        if (entry.codeLevel === "1") {
          this.variableInformation.scanNumber += 1;
          this.variableInformation.small += 1;
        } else if (entry.codeLevel === "2") {
          this.variableInformation.scanNumber +=
            this.fixedInformation.mediumNumber;
          this.variableInformation.medium += 1;
        } else if (entry.codeLevel === "3") {
          this.variableInformation.scanNumber +=
            this.fixedInformation.packingNumber;
          this.variableInformation.big += 1;
        }
      });

      this.variableInformation.needScanNumber =
      this.variableInformation.number - this.variableInformation.scanNumber;

      this.codeLevelChange(true);

      if (this.variableInformation.needScanTime === 0 || this.variableInformation.needScanTime === "0") {
        setTimeout(() => {
          this.submitData = this.submitData.concat(this.packagetableData);
          this.readIndex += 1;
          this.$refs.goodsTable.scrollTo(0, this.readIndex * 40);
          if (this.readIndex === this.tableData.length) {
            this.readIndex -= 1;
            this.getReview(1);
          } else {
            this.$message.success(
              "该批次追溯码已录入完成，请录入下一批次（标蓝）"
            );
            this.dataProcessing();
          }
        }, 700);
      }
    },

    // 提交请求
    getReview(isLast) {
      this.includeLoading = true
      const parameter = {
        productCode: this.defaultInformation.productCode,
        regulatoryCodes: this.submitData,
        orderCode: this.defaultInformation.orderCode,
        mergeOrderCode: this.defaultInformation.mergeOrderCode,
      };
      this.loading = true;
      review(parameter)
        .then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.includeLoading = false
            if(isLast == 1 || isLast == 'pass'){
              this.$emit("regulatoryCodeScanBack", res.result);
              this.close();
            }
            this.$refs.regulatoryCode.focus();
            this.$message.success(res.result.msg || "追溯码，保存成功");
          } else {
            this.resetData();
            this.$message.error(res.msg || "追溯码，保存失败，请重新录入");
          }
        })
        .catch((error) => {
          this.loading = false;
          this.resetData();
          console.log(error);
        });
    },

    // 数据重置
    resetData() {
      this.readIndex = 0;
      this.submitData = [];
      this.dataProcessing();
    },

    // 包装选择器发生变化
    codeLevelChange(value) {
      if (this.formData.codeLevel === "1") {
        this.variableInformation.needScanTime =
          this.variableInformation.needScanNumber;
      } else if (this.formData.codeLevel === "2") {
        this.variableInformation.needScanTime =
          this.variableInformation.needScanNumber /
          this.fixedInformation.mediumNumber;
      } else if (this.formData.codeLevel === "3") {
        this.variableInformation.needScanTime =
          this.variableInformation.needScanNumber /
          this.fixedInformation.packingNumber;
      }

      if (this.variableInformation.needScanTime % 1 !== 0) {
        const number = this.variableInformation.needScanTime.toString();
        this.variableInformation.needScanTime = parseFloat(
          number.substring(0, number.lastIndexOf(".") + 2)
        );
      }

      if (value === true) return;
      if (this.variableInformation.needScanTime < 1) {
        this.$message.warning("当前选择包装类型，其合计数量，超过总数量");
      }
    },

    // 关闭弹窗
    close() {
      this.$refs.dialogTableVisible.close();
    },

    closeDialog() {
      this.$emit("on-before-close");
    },

    // 行颜色
    rowClassName({ row, rowIndex }) {
      if (rowIndex === this.readIndex) {
        return "row--current";
      }
    },

    // 验证替换为整数
    validateNumber(value) {
      this.formData.regulatoryCode = value.replace(/[^0-9]/g, "");
    },

    // 包装类型格式化处理
    codeLevelFormatter({ cellValue }) {
      const data = { 1: "小包装", 2: "中包装", 3: "大包装" };
      return data[cellValue] || "";
    },
  },
};
</script>

<style lang="scss" scoped>
.submitForm {
  margin-top: 20px;
  margin-bottom: 5px;
}
.scanInformation {
  font-size: 14px;
  font-weight: bold;
  margin-top: 5px;
  margin-bottom: 10px;
  .el-col {
    margin-bottom: 10px;
  }
}
.scanInformationFrist {
  font-size: 14px;
  font-weight: bold;
  margin-top: 10px;
}
.viewText {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
